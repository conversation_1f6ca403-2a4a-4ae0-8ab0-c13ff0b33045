import { useState, useEffect } from 'react';
import PulseDash from './Pulse/Pulse';
import CryptoDashboard from './Pulse/Tab';
import Navbar from '@/Home/Navbar/Navbar';
import { activityService } from '@/services/activityService';
import { Search, SlidersHorizontal } from 'lucide-react';
import { ChevronDown } from 'lucide-react';
import Display from './Pulse/Options/Display';
import QuickBuy from './Pulse/Options/QuickBuy';
import TradingSettingsModal from './Pulse/TradingSettiingsModal';
const tabs = ["Pulse", "New", "Trending", "Pre-Launch"];
type SettingsType = {
  greyButtons: boolean;
  circleImages: boolean;
  progressBar: boolean;
  compactTables: boolean;
  // add any other keys that you have here too
};
type CustomRows ={
  marketCap: boolean;
  volume: boolean;
  tx: boolean;
  socials: boolean;
  holders: boolean;
  proTraders: boolean;
  devMigrations: boolean;
}
const Pulse = () => {
  const [activeTab, setActiveTab] = useState("Pulse");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMetric, setSelectedMetric] = useState("MarketCap");
  const [selectedTime, setSelectedTime] = useState("24h");
  const [isOpen2, setIsOpen2] = useState(false);
  
  const metrics = ["MarketCap", "Volume", "Tx"]; // Example metrics
  const timeRanges = ["24h", "7d", "30d"]; // Example time options
  useEffect(() => {
    const session = activityService.getOrCreateSession();
    console.log('Main Pulse component mounted with session:', session);
    activityService.registerPulseActivity();
    return () => {
      activityService.unregisterPulseActivity();
    };
  }, []);
// Display + QuickBuy settings
const [showDropdown, setShowDropdown] = useState(false);
// const [metricsSize, setMetricsSize] = useState('small');
// const [quickBuySize, setQuickBuySize] = useState('small');
const handlePresetClick = () => {
  console.log('Preset button clicked, opening modal. Current preset:', preset);
  setModalOpen(true);
};
const handleAmountChange = (value: string) => setAmount(value);
const handlePresetChange = (presetNumber: number, settings?: any) => {
  console.log('Preset changed to:', presetNumber, 'with settings:', settings);
  setPreset(presetNumber);
};

const [settings, setSettings] = useState<SettingsType>(() => {
  const saved = localStorage.getItem('display_settings');
  return saved ? JSON.parse(saved) : {
    greyButtons: false,
    circleImages: false,
    progressBar: false,
    compactTables: false,
  };
});

const [customRows, setCustomRows] = useState<CustomRows>(() => {
  const saved = localStorage.getItem('custom_rows');
  return saved ? JSON.parse(saved) : {
    marketCap: true,
    volume: true,
    tx: true,
    socials: true,
    holders: true,
    proTraders: true,
    devMigrations: true,
  };
});

const [metricsSize, setMetricsSize] = useState(() => {
  return localStorage.getItem('metrics_size') || 'large';
});

const [quickBuySize, setQuickBuySize] = useState(() => {
  return localStorage.getItem('quick_buy_size') || 'small';
});


const [amount, setAmount] = useState(""); // Entered amount
const [modalOpen, setModalOpen] = useState(false);
const [preset, setPreset] = useState(() => {
  const saved = localStorage.getItem('selected_preset');
  return saved ? parseInt(saved, 10) : 3;
});

// Persist preset changes to localStorage
useEffect(() => {
  localStorage.setItem('selected_preset', preset.toString());
  console.log('Preset persisted to localStorage:', preset);
}, [preset]);

  return (
    <div className="h-screen flex flex-col relative bg-[#141416]">
      {/* App Theme Consistent Background */}
      <div className="absolute inset-0 bg-[#141416]"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-[#181C20]/30 via-transparent to-[#181C20]/20"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-[#00FFBD]/5 via-transparent to-[#025FDA]/5"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-[#00FFBD]/8 via-transparent to-transparent"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-[#025FDA]/8 via-transparent to-transparent"></div>

      {/* Subtle Pattern Overlay */}
      <div className="absolute inset-0 opacity-[0.015]" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.2) 1px, transparent 0)`,
        backgroundSize: '32px 32px'
      }}></div>

      {/* Content Container */}
      <div className="relative z-10 flex flex-col h-full min-h-0">
        <div className="flex-shrink-0">
          <Navbar />
        </div>

        {/* Tabs + Conditional Search UI */}
        <div className="flex-shrink-0 flex justify-between items-center text-white font-medium text-lg pt-6 px-4 mb-4 relative">
          {/* App Theme Consistent Header Background */}
          <div className="absolute inset-0 bg-gradient-to-r from-[#181C20]/95 via-[#141416]/90 to-[#181C20]/95 backdrop-blur-xl border-b border-[#181C20]/60 shadow-xl"></div>
          <div className="flex gap-6 relative z-10">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`relative pb-1 hover:text-white transition-all duration-300 font-semibold ${
                  activeTab === tab ? "text-white" : "text-gray-400"
                }`}
              >
                {tab}
                {activeTab === tab && (
                  <span className="absolute left-0 bottom-0 w-full h-[2px] bg-gradient-to-r from-emerald-400 to-cyan-400 rounded shadow-lg shadow-emerald-400/50" />
                )}
              </button>
            ))}
          </div>

          {/* Only show on "New" or "Pre-Launch" */}
          {(activeTab === "New" || activeTab === "Pre-Launch") && (
            <div className="flex gap-4 items-center p-3 relative z-10">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={16} className="text-slate-400" />
                </div>
                <input
                  type="text"
                  placeholder="Filter by name..."
                  className="bg-[#181C20] backdrop-blur-xl text-white rounded-full pl-10 pr-4 py-2 text-sm w-60 focus:outline-none border border-[#181C20]/80 focus:border-[#00FFBD]/50 transition-all duration-300"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <button className="p-2 rounded-lg bg-[#181C20] backdrop-blur-xl text-slate-300 hover:text-[#00FFBD] border border-[#181C20]/80 hover:border-[#00FFBD]/50 transition-all duration-300">
                <SlidersHorizontal size={18} />
              </button>
            </div>
          )}
          {activeTab === "Trending" && (
            <div className="flex justify-end items-center p-3 text-white rounded-xl relative z-10">
              <div className="flex space-x-4 items-center">
                {/* Metric Toggle Buttons */}
                <div className="flex space-x-3">
                  {metrics.map((metric) => (
                    <button
                      key={metric}
                      onClick={() => setSelectedMetric(metric)}
                      className={`px-4 py-2 border rounded-xl text-sm transition-all duration-300 backdrop-blur-xl ${
                        selectedMetric === metric
                          ? 'bg-emerald-500/90 text-white border-emerald-400 shadow-lg shadow-emerald-400/25'
                          : 'bg-slate-800/60 border-slate-600/50 text-white hover:border-emerald-400/50 hover:bg-slate-700/60'
                      }`}
                    >
                      {metric}
                    </button>
                  ))}
                </div>

                {/* Time Range Dropdown */}
                <div className="relative w-28">
                  <button
                    onClick={() => setIsOpen2(!isOpen2)}
                    className="flex items-center justify-between bg-slate-800/60 backdrop-blur-xl w-full text-slate-300 rounded-xl px-4 py-2 text-sm border border-slate-600/50 hover:border-emerald-400/50 transition-all duration-300"
                  >
                    <span>{selectedTime}</span>
                    <ChevronDown size={16} />
                  </button>

                  {isOpen2 && (
                    <div className="absolute top-full mt-2 w-full bg-slate-800/95 backdrop-blur-xl text-white rounded-xl shadow-2xl border border-slate-600/50 z-50">
                      {timeRanges.map((time) => (
                        <div
                          key={time}
                          onClick={() => {
                            setSelectedTime(time);
                            setIsOpen2(false);
                          }}
                          className={`px-4 py-2 cursor-pointer hover:bg-slate-700/80 rounded-xl transition-all duration-200 ${
                            selectedTime === time ? 'bg-slate-700/80 text-emerald-400' : ''
                          }`}
                        >
                          {time}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Settings Icon */}
                <button className="bg-slate-800/60 backdrop-blur-xl hover:bg-slate-700/60 p-2 rounded-xl border border-slate-600/50 hover:border-emerald-400/50 transition-all duration-300">
                  <SlidersHorizontal className="w-5 h-5 text-slate-300 hover:text-emerald-400 transition-colors duration-300" />
                </button>
              </div>
            </div>
          )}
          {activeTab === "Pulse" && (
            <div className="flex items-center justify-end gap-4 p-3 relative z-50">
              <div className="relative">
                <QuickBuy
                  onAmountChange={handleAmountChange}
                  selectedPreset={preset}
                  onPresetClick={handlePresetClick}
                />
                {modalOpen && (
                  <TradingSettingsModal
                    visible={modalOpen}
                    onClose={() => setModalOpen(false)}
                    selectedPreset={preset}
                    onPresetChange={handlePresetChange}
                  />
                )}
              </div>

              <Display
                metricsSize={metricsSize}
                setMetricsSize={setMetricsSize}
                showDropdown={showDropdown}
                setShowDropdown={setShowDropdown}
                quickBuySize={quickBuySize}
                setQuickBuySize={setQuickBuySize}
                settings={settings}
                setSettings={setSettings}
                customRows={customRows}
                setCustomRows={setCustomRows}
              />
            </div>
          )}
        </div>

        <div className="flex-1 min-h-0 overflow-hidden">
          {activeTab === "Pulse" ? (
            <PulseDash
              settings={settings}
              customRows={customRows}
              metricsSize={metricsSize}
              quickBuySize={quickBuySize}
              amount={amount}
              preset={preset}
              setPreset={setPreset}
              onPresetClick={handlePresetClick}
            />
          ) : (
            <CryptoDashboard activeTab={activeTab} searchTerm={searchTerm} />
          )}
        </div>
      </div>
    </div>
  );
};

export default Pulse;
