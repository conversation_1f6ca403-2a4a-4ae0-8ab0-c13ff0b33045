import { useState, useEffect, useCallback } from "react";
import { usePrivy } from "@privy-io/react-auth";
import { useSolanaWallets } from "@privy-io/react-auth/solana";
import { Setting<PERSON>, Bot } from "lucide-react";
import { getTokenBalance, BalanceRequest, BalanceResponse } from "../../../api/solana_api";

interface QuickBuyProps {
  onAmountChange?: (amount: string) => void;
  selectedPreset: number;
  onPresetClick: () => void;
}

const QuickBuy = ({ onAmountChange, selectedPreset, onPresetClick }: QuickBuyProps) => {
  const [amount, setAmount] = useState("");
  const [balanceData, setBalanceData] = useState<BalanceResponse | null>(null);
  const [isLoadingBalance, setIsLoadingBalance] = useState<boolean>(false);
  const [isAutoTrading, setIsAutoTrading] = useState<boolean>(() => {
    const saved = localStorage.getItem('auto_trading_enabled');
    return saved ? JSON.parse(saved) : false;
  });
  
  // Privy hooks for wallet access
  const { authenticated, user } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();

  // Persist auto trading state
  useEffect(() => {
    localStorage.setItem('auto_trading_enabled', JSON.stringify(isAutoTrading));
  }, [isAutoTrading]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;

    // Validate input - only allow positive numbers
    if (val === '' || (!isNaN(parseFloat(val)) && parseFloat(val) >= 0)) {
      setAmount(val);
      onAmountChange?.(val);
    }
  };

  // Validate amount against balance
  const validateAmount = (inputAmount: string): { isValid: boolean; error?: string } => {
    if (!inputAmount || inputAmount === '') {
      return { isValid: true };
    }

    const numAmount = parseFloat(inputAmount);
    if (isNaN(numAmount) || numAmount <= 0) {
      return { isValid: false, error: 'Please enter a valid amount' };
    }

    if (balanceData?.success) {
      const solBalance = parseFloat(balanceData.data.solBalance);
      if (numAmount > solBalance) {
        return {
          isValid: false,
          error: `Insufficient balance. Available: ${solBalance.toFixed(4)} SOL`
        };
      }
    }

    return { isValid: true };
  };

  const validation = validateAmount(amount);

  // Get Solana wallet address using defaultWallets
  const getSolanaWalletAddress = useCallback(() => {
    if (!authenticated || solanaWallets.length === 0) {
      return null;
    }
    
    // Get the first available Solana wallet
    const solanaWallet = solanaWallets[0];
    return solanaWallet?.address || null;
  }, [authenticated, solanaWallets]);

  // Fetch balance function
  const fetchBalance = useCallback(async () => {
    // Check if user is authenticated
    if (!authenticated) {
      console.log('User not authenticated, cannot fetch balance');
      setBalanceData(null);
      return;
    }

    // Get wallet address
    const walletAddress = getSolanaWalletAddress();
    if (!walletAddress) {
      console.log('No Solana wallet connected, cannot fetch balance');
      setBalanceData(null);
      return;
    }

    // Validate Solana address format
    const isSolanaAddress = (address: string): boolean => {
      const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
      return base58Regex.test(address);
    };

    if (!isSolanaAddress(walletAddress)) {
      console.error('Invalid Solana wallet address format:', walletAddress);
      setBalanceData(null);
      return;
    }

    try {
      setIsLoadingBalance(true);
      
      // For SOL balance we can just send wallet address - the backend is now configured to handle this
      const balanceRequest: BalanceRequest = {
        walletAddress: walletAddress
        // Not including tokenAddress will make the API return only SOL balance
      };

      console.log('Fetching SOL balance for wallet:', walletAddress);
      const response = await getTokenBalance(balanceRequest);
      setBalanceData(response);

      if (!response.success) {
        console.error('Balance fetch failed:', response.error);
      }

    } catch (error) {
      console.error('Error fetching balance:', error);
      setBalanceData(null);
    } finally {
      setIsLoadingBalance(false);
    }
  }, [authenticated, getSolanaWalletAddress]);

  // Get display balance
  const getDisplayBalance = useCallback(() => {
    // Check if user is authenticated and has wallet connected
    if (!authenticated) {
      return '0';
    }

    if (!getSolanaWalletAddress()) {
      return '0';
    }

    // Check if balance data is available and successful
    if (!balanceData?.success) {
      return '0';
    }

    // Show SOL balance
    const solBalance = parseFloat(balanceData.data.solBalance);
    return solBalance.toFixed(4);
  }, [authenticated, balanceData, getSolanaWalletAddress]);

  // Fetch balance when component mounts or wallet changes
  useEffect(() => {
    fetchBalance();

    // Set up listener for wallet changes
    const handleStorageChange = () => {
      fetchBalance();
    };

    // Set up listener for successful quick buy transactions
    const handleQuickBuySuccess = (event: CustomEvent) => {
      console.log('QuickBuy: Received quickBuySuccess event, refreshing balance', event.detail);
      // Refresh balance after successful transaction
      setTimeout(() => {
        fetchBalance();
      }, 1000); // Additional 1 second delay for blockchain confirmation
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('quickBuySuccess', handleQuickBuySuccess as EventListener);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('quickBuySuccess', handleQuickBuySuccess as EventListener);
    };
  }, [fetchBalance]);

  // Set up periodic balance refresh when user is authenticated and has wallet
  useEffect(() => {
    if (!authenticated || !getSolanaWalletAddress()) {
      return;
    }

    // Refresh balance every 30 seconds when user is active
    const intervalId = setInterval(() => {
      console.log('QuickBuy: Periodic balance refresh');
      fetchBalance();
    }, 30000); // 30 seconds

    return () => {
      clearInterval(intervalId);
    };
  }, [authenticated, getSolanaWalletAddress, fetchBalance]);

  return (
    <div className="relative flex items-center gap-3">
      {/* Quick Buy Input */}
      <div className={`flex items-center bg-gradient-to-r from-[#1A1A1A] to-[#1F1F1F] border rounded-full px-4 py-2 text-white text-sm space-x-3 shadow-md transition-all duration-300 ${
        !validation.isValid
          ? 'border-red-500/60 shadow-red-500/20'
          : 'border-gray-600/50 hover:border-emerald-400/40 hover:shadow-lg hover:shadow-emerald-400/10'
      }`}>
        <span className="font-medium text-gray-300 text-xs">Quick Buy</span>

        {/* Input field */}
        <input
          type="number"
          placeholder="Amount"
          value={amount}
          onChange={handleInputChange}
          className={`bg-transparent placeholder-gray-500 border-none outline-none text-white px-2 py-1 rounded-lg w-20 font-medium text-sm
                     appearance-none [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none
                     focus:bg-gray-800/20 transition-colors duration-200 ${
                       !validation.isValid ? 'text-red-400' : ''
                     }`}
        />

        {/* SOL Balance indicator with left border - non-clickable */}
        <div className="flex items-center space-x-1.5 px-2 py-1 rounded-lg border-l border-gray-600/50 pl-3">
          <div className="flex items-center space-x-1.5">
            <img
              src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
              alt="Solana"
              className="w-3.5 h-3.5 rounded-full"
            />
            <span className="text-gray-300 text-xs font-medium">
              {isLoadingBalance ? (
                <div className="animate-pulse text-emerald-400">...</div>
              ) : (
                `${getDisplayBalance()} SOL`
              )}
            </span>
          </div>
        </div>
      </div>

      {/* Preset Button - Shows current preset number */}
      <button
        onClick={() => {
          console.log('QuickBuy: Preset button clicked, auto trading:', isAutoTrading, 'preset:', selectedPreset);
          onPresetClick();
        }}
        className={`flex items-center gap-2 rounded-full px-4 py-2 text-sm font-medium transition-all duration-300 group ${
          isAutoTrading
            ? 'bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-400/60 text-emerald-300 shadow-lg shadow-emerald-400/20'
            : 'bg-gradient-to-r from-[#1A1A1A] to-[#1F1F1F] border border-gray-600/50 text-white hover:border-emerald-400/50 hover:shadow-lg hover:shadow-emerald-400/10'
        }`}
      >
        <Settings className={`w-4 h-4 transition-colors duration-300 ${
          isAutoTrading
            ? 'text-emerald-400'
            : 'text-gray-400 group-hover:text-emerald-400'
        }`} />
        <span>Preset {selectedPreset}</span>
        {isAutoTrading && (
          <span className="text-xs px-1.5 py-0.5 rounded-full font-normal bg-emerald-400/20 text-emerald-300 border border-emerald-400/30">
            auto
          </span>
        )}
      </button>

      {/* Auto Trading Toggle */}
      <button
        onClick={() => {
          const newAutoState = !isAutoTrading;
          console.log('QuickBuy: Auto trading toggled to:', newAutoState);
          setIsAutoTrading(newAutoState);
        }}
        className={`flex items-center gap-2 rounded-full px-4 py-2 text-sm font-medium transition-all duration-500 transform ${
          isAutoTrading
            ? 'bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-400/60 text-emerald-300 shadow-lg shadow-emerald-400/20 scale-105'
            : 'bg-gradient-to-r from-[#1A1A1A] to-[#1F1F1F] border border-gray-600/50 text-gray-400 hover:border-blue-400/50 hover:text-blue-300 hover:shadow-lg hover:shadow-blue-400/10 hover:scale-105'
        }`}
      >
        <Bot className={`w-4 h-4 transition-all duration-500 ${
          isAutoTrading ? 'text-emerald-400 animate-pulse' : 'text-gray-500'
        }`} />
        <span className="transition-all duration-300">Auto</span>
        <span className={`text-xs px-1.5 py-0.5 rounded-full font-normal transition-all duration-500 ${
          isAutoTrading
            ? 'bg-emerald-400/20 text-emerald-300 border border-emerald-400/30 animate-pulse'
            : 'bg-gray-600/30 text-gray-500 border border-gray-600/50'
        }`}>
          beta
        </span>
      </button>

      {/* Error message */}
      {!validation.isValid && validation.error && (
        <div className="absolute top-full mt-1.5 left-0 text-red-300 text-xs bg-gradient-to-r from-red-900/80 to-red-800/80 border border-red-500/50 rounded-lg px-2.5 py-1.5 z-10 shadow-md shadow-red-500/20 backdrop-blur-sm">
          {validation.error}
        </div>
      )}
    </div>
  );
};

export default QuickBuy;

