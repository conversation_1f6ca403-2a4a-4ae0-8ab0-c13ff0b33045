import { useState, useEffect } from 'react';
import { LockIcon, X } from 'lucide-react';

type TradingSettingsModalProps = {
  selectedPreset?: number;
  onPresetChange?: (preset: number, settings: SettingsObject) => void;
  visible: boolean;
  onClose: () => void;
  initialAutoFee?: boolean;
  initialLockPreset?: boolean;
};

type SettingsObject = {
  slippage: string;
  priority: string;
  bribe: string;
};

type PresetSettings = {
  [key: number]: SettingsObject;
};

export default function TradingSettingsModal({
  selectedPreset = 3,
  onPresetChange,
  visible,
  onClose,
  initialAutoFee = false,
  initialLockPreset = true,
}: TradingSettingsModalProps) {
  // Default settings for each preset
  const defaultPresetSettings: PresetSettings = {
    1: { slippage: '1', priority: '0.001', bribe: '0.001' },
    2: { slippage: '5', priority: '0.005', bribe: '0.005' },
    3: { slippage: '10', priority: '0.01', bribe: '0.01' }
  };

  const [preset, setPreset] = useState(selectedPreset);
  const [buySettings, setBuySettings] = useState(true);
  const [autoFee, setAutoFee] = useState(initialAutoFee);
  const [lockPreset, setLockPreset] = useState(initialLockPreset);
  const [mevMode, setMevMode] = useState(() => {
    const saved = localStorage.getItem('mev_mode');
    return saved || 'Off';
  });
  const [rpcUrl, setRpcUrl] = useState('');

  // State for storing settings for each preset
  const [presetSettings, setPresetSettings] = useState<PresetSettings>(() => {
    const saved = localStorage.getItem('preset_settings');
    return saved ? JSON.parse(saved) : {
      1: { ...defaultPresetSettings[1] },
      2: { ...defaultPresetSettings[2] },
      3: { ...defaultPresetSettings[3] }
    };
  });

  // Current settings based on selected preset
  const [currentSettings, setCurrentSettings] = useState(presetSettings[preset]);

  useEffect(() => {
    setPreset(selectedPreset);
    setCurrentSettings(presetSettings[selectedPreset]);
  }, [selectedPreset, presetSettings]);

  // Persist settings
  useEffect(() => {
    localStorage.setItem('preset_settings', JSON.stringify(presetSettings));
  }, [presetSettings]);

  useEffect(() => {
    localStorage.setItem('mev_mode', mevMode);
  }, [mevMode]);

  if (!visible) return null;

  console.log('TradingSettingsModal: Rendering modal with preset:', preset, 'selectedPreset:', selectedPreset);

  // Handle preset selection
  const handlePresetClick = (value: number) => {
    setPreset(value);
    setCurrentSettings(presetSettings[value]);
    onPresetChange?.(value, presetSettings[value]);
  };

  // Handle settings change
  const handleSettingChange = (field: keyof SettingsObject, value: string) => {
    const updatedSettings = { ...currentSettings, [field]: value };
    setCurrentSettings(updatedSettings);
    
    // Update the preset settings object
    setPresetSettings({
      ...presetSettings,
      [preset]: updatedSettings
    });
    
    // Notify parent component if needed
    onPresetChange?.(preset, updatedSettings);
  };

  // Handle form submission
  const handleSubmit = () => {
    console.log('Selected Preset:', preset);
    console.log('Settings:', presetSettings[preset]);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-500 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4">
      <div className="bg-gradient-to-br from-[#141416] via-[#1A1A1A] to-[#141416] border border-gray-600/30 rounded-2xl p-8 w-full max-w-lg shadow-2xl shadow-black/50 relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-transparent to-blue-500/5"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-emerald-400/8 via-transparent to-transparent"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-blue-400/8 via-transparent to-transparent"></div>

        {/* Content */}
        <div className="relative z-10">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-emerald-500 to-blue-500 flex items-center justify-center">
                <span className="text-white text-sm">⚙️</span>
              </div>
              <h2 className="text-xl font-bold text-white">Trading Settings</h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-700/30 rounded-lg"
            >
              <X size={20} />
            </button>
          </div>

          {/* Preset Buttons */}
          <div className="mb-6">
            <label className="block text-gray-300 text-sm font-medium mb-3">Select Preset</label>
            <div className="flex gap-2 bg-gray-800/30 rounded-xl p-1.5 backdrop-blur-sm border border-gray-600/20">
              {[1, 2, 3].map((p) => (
                <button
                  key={p}
                  className={`flex-1 py-3 text-sm font-semibold rounded-lg transition-all duration-300 ${
                    preset === p
                      ? 'bg-gradient-to-r from-emerald-500 to-blue-500 text-white shadow-lg shadow-emerald-500/25'
                      : 'text-gray-300 hover:bg-gray-700/50 hover:text-white'
                  }`}
                  onClick={() => handlePresetClick(p)}
                >
                  PRESET {p}
                </button>
              ))}
            </div>
          </div>

          {/* Buy/Sell Toggle */}
          <div className="mb-6">
            <label className="block text-gray-300 text-sm font-medium mb-3">Trading Mode</label>
            <div className="flex gap-2 bg-gray-800/30 rounded-xl p-1.5 backdrop-blur-sm border border-gray-600/20">
              <button
                className={`flex-1 py-3 text-sm font-semibold rounded-lg transition-all duration-300 ${
                  buySettings
                    ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg shadow-green-500/25'
                    : 'text-gray-300 hover:bg-gray-700/50 hover:text-white'
                }`}
                onClick={() => setBuySettings(true)}
              >
                Buy Settings
              </button>
              <button
                className={`flex-1 py-3 text-sm font-semibold rounded-lg transition-all duration-300 ${
                  !buySettings
                    ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg shadow-red-500/25'
                    : 'text-gray-300 hover:bg-gray-700/50 hover:text-white'
                }`}
                onClick={() => setBuySettings(false)}
              >
                Sell Settings
              </button>
            </div>
          </div>

          {/* Input Fields */}
          <div className="mb-6">
            <label className="block text-gray-300 text-sm font-medium mb-3">Configuration</label>
            <div className="grid grid-cols-3 gap-4">
              {[
                { label: 'SLIPPAGE', key: 'slippage', unit: '%' },
                { label: 'PRIORITY', key: 'priority', unit: 'SOL' },
                { label: 'BRIBE', key: 'bribe', unit: 'SOL' },
              ].map((field) => (
                <div key={field.label} className="flex flex-col">
                  <label className="text-gray-400 mb-2 text-xs font-medium uppercase tracking-wide">{field.label}</label>
                  <div className="relative">
                    <input
                      type="text"
                      value={currentSettings[field.key as keyof SettingsObject]}
                      onChange={(e) => handleSettingChange(field.key as keyof SettingsObject, e.target.value)}
                      className="w-full text-sm bg-gray-800/40 border border-gray-600/30 rounded-lg px-3 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-300 backdrop-blur-sm"
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs font-medium">
                      {field.unit}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* MEV Mode */}
          <div className="mb-6">
            <label className="block text-gray-300 text-sm font-medium mb-3">MEV Protection</label>
            <select
              value={mevMode}
              onChange={(e) => setMevMode(e.target.value)}
              className="w-full text-sm bg-gray-800/40 border border-gray-600/30 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-300 backdrop-blur-sm"
            >
              <option value="Off" className="bg-gray-800">Off</option>
              <option value="Low" className="bg-gray-800">Low</option>
              <option value="Medium" className="bg-gray-800">Medium</option>
              <option value="High" className="bg-gray-800">High</option>
            </select>
          </div>

          {/* Advanced Options */}
          <div className="mb-6">
            <label className="block text-gray-300 text-sm font-medium mb-3">Advanced Options</label>
            <div className="space-y-3">
              <label className="flex items-center space-x-3 p-3 rounded-lg bg-gray-800/20 border border-gray-600/20 hover:bg-gray-800/30 transition-colors cursor-pointer">
                <input
                  type="checkbox"
                  className="w-4 h-4 text-emerald-500 bg-gray-700 border-gray-600 rounded focus:ring-emerald-500 focus:ring-2"
                  checked={autoFee}
                  onChange={(e) => setAutoFee(e.target.checked)}
                />
                <span className="text-gray-300 text-sm">Auto Fee</span>
                <span className="text-xs px-2 py-1 rounded-full bg-blue-500/20 text-blue-300 border border-blue-500/30">
                  beta
                </span>
              </label>
              <label className="flex items-center space-x-3 p-3 rounded-lg bg-gray-800/20 border border-gray-600/20 hover:bg-gray-800/30 transition-colors cursor-pointer">
                <input
                  type="checkbox"
                  className="w-4 h-4 text-emerald-500 bg-gray-700 border-gray-600 rounded focus:ring-emerald-500 focus:ring-2"
                  checked={lockPreset}
                  onChange={(e) => setLockPreset(e.target.checked)}
                />
                <span className="flex items-center text-emerald-400 text-sm">
                  <LockIcon size={16} className="mr-2" />
                  Lock Preset for Pulse
                </span>
              </label>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              className="flex-1 py-3 text-sm font-semibold bg-gray-700/50 text-gray-300 rounded-lg hover:bg-gray-700/70 transition-all duration-300"
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              className="flex-1 py-3 text-sm font-semibold bg-gradient-to-r from-emerald-500 to-blue-500 text-white rounded-lg hover:from-emerald-600 hover:to-blue-600 transition-all duration-300 shadow-lg shadow-emerald-500/25"
              onClick={handleSubmit}
            >
              Continue
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}